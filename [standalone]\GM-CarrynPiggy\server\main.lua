ESX = exports["es_extended"]:getSharedObject()

RegisterServerEvent('GMCarry:sync')
AddEventHandler('GMCarry:sync', function(target, animationLib, animationLib2, animation, animation2, distans, distans2, height, targetSrc, length, spin, controlFlagSrc, controlFlagTarget, animFlagTarget)
    TriggerClientEvent('GMCarry:syncTarget', targetSrc, source, animationLib2, animation2, distans, distans2, height, length, spin, controlFlagTarget)
    TriggerClientEvent('GMCarry:syncMe', source, animationLib, animation, length, controlFlagSrc)
end)

RegisterServerEvent('GMCarry:stop')
AddEventHandler('GMCarry:stop', function(targetSrc)
    TriggerClientEvent('GMCarry:cl_stop', targetSrc)
end)

RegisterServerEvent('GMCarry:syncPiggy')
AddEventHandler('GMCarry:syncPiggy', function(target, animationLib, animation, animation2, distans, distans2, height, targetSrc, length, spin, controlFlagSrc, controlFlagTarget, animFlagTarget)
    TriggerClientEvent('GMCarry:syncPiggyTarget', targetSrc, source, animationLib, animation2, distans, distans2, height, length, spin, controlFlagTarget)
    TriggerClientEvent('GMCarry:syncPiggyMe', source, animationLib, animation, length, controlFlagSrc)
end)

RegisterServerEvent('GMCarry:stopPiggy')
AddEventHandler('GMCarry:stopPiggy', function(targetSrc)
    TriggerClientEvent('GMCarry:cl_stopPiggy', targetSrc)
end)

print('GM-CarrynPiggy 1.0 - Converted to new ESX')

