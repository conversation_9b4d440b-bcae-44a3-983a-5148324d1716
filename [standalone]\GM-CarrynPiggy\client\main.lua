local piggyBackInProgress = false
local carryingBackInProgress = false

ESX = exports["es_extended"]:getSharedObject()

RegisterCommand('carry', function()
    if not carryingBackInProgress then
        carryingBackInProgress = true
        local lib = 'missfinale_c2mcs_1'
        local anim1 = 'fin_c2_mcs_1_camman'
        local lib2 = 'nm'
        local anim2 = 'firemans_carry'
        local distans = 0.15
        local distans2 = 0.27
        local height = 0.63
        local spin = 0.0
        local length = 100000
        local controlFlagMe = 49
        local controlFlagTarget = 33
        local animFlagTarget = 1
        local closestPlayer = GetClosestPlayer(3)
        local target = GetPlayerServerId(closestPlayer)

        if closestPlayer ~= nil then
            TriggerServerEvent('GMCarry:sync', closestPlayer, lib, lib2, anim1, anim2, distans, distans2, height, target, length, spin, controlFlagMe, controlFlagTarget, animFlagTarget)
        else
            ESX.ShowNotification("No player nearby")
        end
    else
        carryingBackInProgress = false
        ClearPedSecondaryTask(PlayerPedId())
        DetachEntity(PlayerPedId(), true, false)
        local closestPlayer = GetClosestPlayer(3)
        local target = GetPlayerServerId(closestPlayer)
        TriggerServerEvent("GMCarry:stop", target)
    end
end, false)

RegisterCommand('pb', function()
    if not piggyBackInProgress then
        piggyBackInProgress = true
        local lib = 'anim@arena@celeb@flat@paired@no_props@'
        local anim1 = 'piggyback_c_player_a'
        local anim2 = 'piggyback_c_player_b'
        local distans = -0.07
        local distans2 = 0.0
        local height = 0.45
        local spin = 0.0
        local length = 100000
        local controlFlagMe = 49
        local controlFlagTarget = 33
        local animFlagTarget = 1
        local closestPlayer = GetClosestPlayer(3)
        local target = GetPlayerServerId(closestPlayer)

        if closestPlayer ~= nil then
            TriggerServerEvent('GMCarry:syncPiggy', closestPlayer, lib, anim1, anim2, distans, distans2, height, target, length, spin, controlFlagMe, controlFlagTarget, animFlagTarget)
        else
            ESX.ShowNotification("No player nearby")
        end
    else
        piggyBackInProgress = false
        ClearPedSecondaryTask(PlayerPedId())
        DetachEntity(PlayerPedId(), true, false)
        local closestPlayer = GetClosestPlayer(3)
        local target = GetPlayerServerId(closestPlayer)
        TriggerServerEvent("GMCarry:stopPiggy", target)
    end
end, false)

RegisterNetEvent('GMCarry:syncTarget')
AddEventHandler('GMCarry:syncTarget', function(target, animationLib, animation2, distans, distans2, height, length, spin, controlFlag)
    local playerPed = PlayerPedId()
    local targetPed = GetPlayerPed(GetPlayerFromServerId(target))
    carryingBackInProgress = true

    RequestAnimDict(animationLib)
    while not HasAnimDictLoaded(animationLib) do
        Citizen.Wait(10)
    end

    if spin == nil then spin = 180.0 end
    AttachEntityToEntity(PlayerPedId(), targetPed, 0, distans2, distans, height, 0.5, 0.5, spin, false, false, false, false, 2, false)
    if controlFlag == nil then controlFlag = 0 end
    TaskPlayAnim(playerPed, animationLib, animation2, 8.0, -8.0, length, controlFlag, 0, false, false, false)
end)

RegisterNetEvent('GMCarry:syncMe')
AddEventHandler('GMCarry:syncMe', function(animationLib, animation, length, controlFlag)
    local playerPed = PlayerPedId()
    RequestAnimDict(animationLib)
    while not HasAnimDictLoaded(animationLib) do
        Citizen.Wait(10)
    end

    if controlFlag == nil then controlFlag = 0 end
    TaskPlayAnim(playerPed, animationLib, animation, 8.0, -8.0, length, controlFlag, 0, false, false, false)
end)

RegisterNetEvent('GMCarry:cl_stop')
AddEventHandler('GMCarry:cl_stop', function()
    carryingBackInProgress = false
    ClearPedSecondaryTask(PlayerPedId())
    DetachEntity(PlayerPedId(), true, false)
end)

function GetPlayers()
    local players = {}
    for i = 0, 255 do
        if NetworkIsPlayerActive(i) then
            table.insert(players, i)
        end
    end
    return players
end

function GetClosestPlayer(radius)
    local players = GetPlayers()
    local closestDistance = -1
    local closestPlayer = -1
    local ply = PlayerPedId()
    local plyCoords = GetEntityCoords(ply, false)

    for _, value in ipairs(players) do
        local target = GetPlayerPed(value)
        if target ~= ply then
            local targetCoords = GetEntityCoords(GetPlayerPed(value), false)
            local distance = GetDistanceBetweenCoords(targetCoords['x'], targetCoords['y'], targetCoords['z'], plyCoords['x'], plyCoords['y'], plyCoords['z'], true)
            if closestDistance == -1 or closestDistance > distance then
                closestPlayer = value
                closestDistance = distance
            end
        end
    end

    if closestDistance <= radius then
        return closestPlayer
    else
        return nil
    end
end

RegisterNetEvent('GMCarry:syncPiggyTarget')
AddEventHandler('GMCarry:syncPiggyTarget', function(target, animationLib, animation2, distans, distans2, height, length, spin, controlFlag)
    local playerPed = PlayerPedId()
    local targetPed = GetPlayerPed(GetPlayerFromServerId(target))
    piggyBackInProgress = true

    RequestAnimDict(animationLib)
    while not HasAnimDictLoaded(animationLib) do
        Citizen.Wait(10)
    end

    if spin == nil then spin = 180.0 end
    AttachEntityToEntity(PlayerPedId(), targetPed, 0, distans2, distans, height, 0.5, 0.5, spin, false, false, false, false, 2, false)
    if controlFlag == nil then controlFlag = 0 end
    TaskPlayAnim(playerPed, animationLib, animation2, 8.0, -8.0, length, controlFlag, 0, false, false, false)
end)

RegisterNetEvent('GMCarry:syncPiggyMe')
AddEventHandler('GMCarry:syncPiggyMe', function(animationLib, animation, length, controlFlag)
    local playerPed = PlayerPedId()
    RequestAnimDict(animationLib)
    while not HasAnimDictLoaded(animationLib) do
        Citizen.Wait(10)
    end

    if controlFlag == nil then controlFlag = 0 end
    TaskPlayAnim(playerPed, animationLib, animation, 8.0, -8.0, length, controlFlag, 0, false, false, false)
end)

RegisterNetEvent('GMCarry:cl_stopPiggy')
AddEventHandler('GMCarry:cl_stopPiggy', function()
    piggyBackInProgress = false
    ClearPedSecondaryTask(PlayerPedId())
    DetachEntity(PlayerPedId(), true, false)
end)
